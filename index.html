<!DOCTYPE html>
<html lang="en">

<head>
    <title>three.js ar - hit test</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <link type="text/css" rel="stylesheet" href="main.css">
</head>

<body>

    <!-- QR Scanner Interface -->
    <div id="qr-scanner-container" style="display: block;">
        <div id="qr-info"
            style="text-align: center; padding: 20px; background: rgba(0,0,0,0.8); color: white; position: fixed; top: 0; left: 0; right: 0; z-index: 1000;">
            <h2>Quét mã QR để vào chức năng AR</h2>
            <p>Hướng camera vào mã QR AR để tiếp tục</p>
        </div>
        <div id="qr-reader" style="width: 100%; height: 100vh;"></div>
        <div id="qr-result"
            style="display: none; position: fixed; bottom: 20px; left: 20px; right: 20px; background: rgba(0,0,0,0.8); color: white; padding: 15px; border-radius: 10px; text-align: center; z-index: 1001;">
            <p id="qr-message"></p>
            <button id="retry-btn" onclick="retryQRScan()"
                style="margin-top: 10px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">Thử
                lại</button>
        </div>
    </div>

    <!-- AR Interface (hidden initially) -->
    <div id="ar-container" style="display: none;">
        <div id="info">
            <a href="https://threejs.org" target="_blank" rel="noopener">three.js</a> ar - hit test<br />
            <button id="back-to-qr" onclick="backToQRScanner()"
                style="margin-top: 10px; padding: 5px 10px; background: #dc3545; color: white; border: none; border-radius: 3px; cursor: pointer;">Quay
                lại quét QR</button>
        </div>
    </div>

    <!-- QR Code Scanner Library -->
    <script src="https://unpkg.com/html5-qrcode@2.3.8/minified/html5-qrcode.min.js"></script>

    <script type="importmap">
        {
            "imports": {
                "three": "https://cdn.jsdelivr.net/npm/three@0.132.2/build/three.module.js",
                "three/addons/": "https://cdn.jsdelivr.net/npm/three@0.132.2/examples/jsm/"
            }
        }
    </script>

    <!-- QR Scanner Script -->
    <script>
        let html5QrcodeScanner;
        let isARInitialized = false;

        // Danh sách các mã QR hợp lệ cho AR (có thể tùy chỉnh)
        const validARCodes = [
            'AR_PLACE_ITEM',
            'AR_FURNITURE',
            'AR_3D_MODEL',
            'WEBXR_AR',
            'THREE_JS_AR'
        ];

        function initQRScanner() {
            html5QrcodeScanner = new Html5QrcodeScanner(
                "qr-reader",
                {
                    fps: 10,
                    qrbox: { width: 250, height: 250 },
                    aspectRatio: 1.0
                },
                false
            );
            html5QrcodeScanner.render(onScanSuccess, onScanFailure);
        }

        function onScanSuccess(decodedText, decodedResult) {
            console.log(`QR Code detected: ${decodedText}`);

            // Kiểm tra xem mã QR có hợp lệ cho AR không
            if (isValidARCode(decodedText)) {
                showQRResult(`✅ Mã QR hợp lệ! Đang khởi tạo AR...`, 'success');
                setTimeout(() => {
                    startARExperience();
                }, 2000);
            } else {
                showQRResult(`❌ Mã QR không hợp lệ cho AR. Vui lòng quét mã QR AR đúng.`, 'error');
            }

            // Dừng scanner
            html5QrcodeScanner.clear();
        }

        function onScanFailure(error) {
            // Không cần hiển thị lỗi liên tục
            // console.warn(`QR Code scan error: ${error}`);
        }

        function isValidARCode(code) {
            // Kiểm tra xem mã có trong danh sách hợp lệ không
            // Hoặc có chứa từ khóa AR
            return validARCodes.includes(code) ||
                code.toUpperCase().includes('AR') ||
                code.toUpperCase().includes('WEBXR') ||
                code.toUpperCase().includes('3D');
        }

        function showQRResult(message, type) {
            const resultDiv = document.getElementById('qr-result');
            const messageP = document.getElementById('qr-message');
            const retryBtn = document.getElementById('retry-btn');

            messageP.textContent = message;
            resultDiv.style.display = 'block';

            if (type === 'success') {
                resultDiv.style.background = 'rgba(40, 167, 69, 0.9)';
                retryBtn.style.display = 'none';
            } else {
                resultDiv.style.background = 'rgba(220, 53, 69, 0.9)';
                retryBtn.style.display = 'inline-block';
            }
        }

        function retryQRScan() {
            document.getElementById('qr-result').style.display = 'none';
            initQRScanner();
        }

        function startARExperience() {
            // Ẩn QR scanner
            document.getElementById('qr-scanner-container').style.display = 'none';
            // Hiển thị AR container
            document.getElementById('ar-container').style.display = 'block';

            // Khởi tạo AR nếu chưa được khởi tạo
            if (!isARInitialized) {
                initAR();
                isARInitialized = true;
            }
        }

        function backToQRScanner() {
            // Ẩn AR container
            document.getElementById('ar-container').style.display = 'none';
            // Hiển thị QR scanner
            document.getElementById('qr-scanner-container').style.display = 'block';
            // Khởi tạo lại QR scanner
            document.getElementById('qr-result').style.display = 'none';
            initQRScanner();
        }

        // Khởi tạo QR scanner khi trang load
        window.addEventListener('load', function () {
            initQRScanner();
        });
    </script>

    <script type="module">

        import * as THREE from 'three';
        import { ARButton } from 'three/addons/webxr/ARButton.js';

        let container;
        let camera, scene, renderer;
        let controller1, controller2;

        let reticle;

        let hitTestSource = null;
        let hitTestSourceRequested = false;

        // Không tự động khởi tạo AR, chỉ khởi tạo khi quét QR thành công
        // init();

        function initAR() {

            container = document.getElementById('ar-container');
            if (!container) {
                container = document.createElement('div');
                document.body.appendChild(container);
            }

            scene = new THREE.Scene();

            camera = new THREE.PerspectiveCamera(70, window.innerWidth / window.innerHeight, 0.01, 20);

            const light = new THREE.HemisphereLight(0xffffff, 0xbbbbff, 3);
            light.position.set(0.5, 1, 0.25);
            scene.add(light);

            //

            renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
            renderer.setPixelRatio(window.devicePixelRatio);
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setAnimationLoop(animate);
            renderer.xr.enabled = true;
            container.appendChild(renderer.domElement);

            //

            container.appendChild(ARButton.createButton(renderer, { requiredFeatures: ['hit-test'] }));

            //

            const geometry = new THREE.CylinderGeometry(0.1, 0.1, 0.2, 32).translate(0, 0.1, 0);

            function onSelect() {

                if (reticle.visible) {

                    const material = new THREE.MeshPhongMaterial({ color: 0xffffff * Math.random() });
                    const mesh = new THREE.Mesh(geometry, material);
                    reticle.matrix.decompose(mesh.position, mesh.quaternion, mesh.scale);
                    mesh.scale.y = Math.random() * 2 + 1;
                    scene.add(mesh);

                }

            }

            controller1 = renderer.xr.getController(0);
            controller1.addEventListener('select', onSelect);
            scene.add(controller1);

            controller2 = renderer.xr.getController(1);
            controller2.addEventListener('select', onSelect);
            scene.add(controller2);

            reticle = new THREE.Mesh(
                new THREE.RingGeometry(0.15, 0.2, 32).rotateX(- Math.PI / 2),
                new THREE.MeshBasicMaterial()
            );
            reticle.matrixAutoUpdate = false;
            reticle.visible = false;
            scene.add(reticle);

            //

            window.addEventListener('resize', onWindowResize);

        }

        function onWindowResize() {

            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();

            renderer.setSize(window.innerWidth, window.innerHeight);

        }

        //

        function animate(timestamp, frame) {

            if (frame) {

                const referenceSpace = renderer.xr.getReferenceSpace();
                const session = renderer.xr.getSession();

                if (hitTestSourceRequested === false) {

                    session.requestReferenceSpace('viewer').then(function (referenceSpace) {

                        session.requestHitTestSource({ space: referenceSpace }).then(function (source) {

                            hitTestSource = source;

                        });

                    });

                    session.addEventListener('end', function () {

                        hitTestSourceRequested = false;
                        hitTestSource = null;

                    });

                    hitTestSourceRequested = true;

                }

                if (hitTestSource) {

                    const hitTestResults = frame.getHitTestResults(hitTestSource);

                    if (hitTestResults.length) {

                        const hit = hitTestResults[0];

                        reticle.visible = true;
                        reticle.matrix.fromArray(hit.getPose(referenceSpace).transform.matrix);

                    } else {

                        reticle.visible = false;

                    }

                }

            }

            renderer.render(scene, camera);

        }

    </script>
</body>

</html>