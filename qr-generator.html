<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tạo mã QR cho AR</title>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .qr-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            text-align: center;
        }
        .qr-section h3 {
            color: #555;
            margin-bottom: 15px;
        }
        .qr-code {
            margin: 15px 0;
        }
        .qr-text {
            font-family: monospace;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            word-break: break-all;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .custom-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Tạo mã QR cho ứng dụng AR</h1>
        
        <div class="qr-section">
            <h3>📱 Mã QR hợp lệ cho AR</h3>
            <p>Các mã QR này sẽ cho phép truy cập vào chức năng AR:</p>
            
            <div id="valid-qr-codes"></div>
        </div>

        <div class="qr-section">
            <h3>🚫 Mã QR không hợp lệ (để test)</h3>
            <div id="invalid-qr-codes"></div>
        </div>

        <div class="qr-section">
            <h3>✏️ Tạo mã QR tùy chỉnh</h3>
            <input type="text" id="custom-text" class="custom-input" placeholder="Nhập nội dung mã QR...">
            <button onclick="generateCustomQR()">Tạo mã QR</button>
            <div id="custom-qr"></div>
        </div>
    </div>

    <script>
        // Danh sách mã QR hợp lệ
        const validCodes = [
            'AR_PLACE_ITEM',
            'AR_FURNITURE', 
            'AR_3D_MODEL',
            'WEBXR_AR',
            'THREE_JS_AR'
        ];

        // Danh sách mã QR không hợp lệ để test
        const invalidCodes = [
            'NORMAL_QR',
            'WEBSITE_LINK',
            'CONTACT_INFO'
        ];

        function generateQRCode(text, containerId) {
            const container = document.getElementById(containerId);
            const qrDiv = document.createElement('div');
            qrDiv.className = 'qr-code';
            
            const canvas = document.createElement('canvas');
            QRCode.toCanvas(canvas, text, { width: 200, margin: 2 }, function (error) {
                if (error) console.error(error);
            });
            
            const textDiv = document.createElement('div');
            textDiv.className = 'qr-text';
            textDiv.textContent = text;
            
            const downloadBtn = document.createElement('button');
            downloadBtn.textContent = 'Tải xuống';
            downloadBtn.onclick = () => downloadQR(canvas, text);
            
            qrDiv.appendChild(canvas);
            qrDiv.appendChild(textDiv);
            qrDiv.appendChild(downloadBtn);
            container.appendChild(qrDiv);
        }

        function downloadQR(canvas, filename) {
            const link = document.createElement('a');
            link.download = `qr-${filename}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }

        function generateCustomQR() {
            const text = document.getElementById('custom-text').value.trim();
            if (!text) {
                alert('Vui lòng nhập nội dung cho mã QR!');
                return;
            }
            
            const container = document.getElementById('custom-qr');
            container.innerHTML = ''; // Clear previous QR
            
            const canvas = document.createElement('canvas');
            QRCode.toCanvas(canvas, text, { width: 200, margin: 2 }, function (error) {
                if (error) console.error(error);
            });
            
            const textDiv = document.createElement('div');
            textDiv.className = 'qr-text';
            textDiv.textContent = text;
            
            const downloadBtn = document.createElement('button');
            downloadBtn.textContent = 'Tải xuống';
            downloadBtn.onclick = () => downloadQR(canvas, text);
            
            container.appendChild(canvas);
            container.appendChild(textDiv);
            container.appendChild(downloadBtn);
        }

        // Tạo các mã QR khi trang load
        window.addEventListener('load', function() {
            // Tạo mã QR hợp lệ
            validCodes.forEach(code => {
                generateQRCode(code, 'valid-qr-codes');
            });
            
            // Tạo mã QR không hợp lệ
            invalidCodes.forEach(code => {
                generateQRCode(code, 'invalid-qr-codes');
            });
        });
    </script>
</body>
</html>
